{"cells": [{"cell_type": "code", "execution_count": 1, "id": "a82fdf50", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["quantback init\n", "datetime       datetime64[s]\n", "stock_code    string[python]\n", "open                 float64\n", "high                 float64\n", "low                  float64\n", "close                float64\n", "pre_close            float64\n", "volume               float64\n", "amount               float64\n", "high_limit           float64\n", "low_limit            float64\n", "dtype: object\n"]}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>stock_code</th>\n", "      <th>open</th>\n", "      <th>high</th>\n", "      <th>low</th>\n", "      <th>close</th>\n", "      <th>pre_close</th>\n", "      <th>volume</th>\n", "      <th>amount</th>\n", "      <th>high_limit</th>\n", "      <th>low_limit</th>\n", "    </tr>\n", "    <tr>\n", "      <th>datetime</th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>2024-12-02</th>\n", "      <td>600519.SH</td>\n", "      <td>1502.505324</td>\n", "      <td>1506.433893</td>\n", "      <td>1491.773143</td>\n", "      <td>1501.520720</td>\n", "      <td>1502.249327</td>\n", "      <td>2681954.0</td>\n", "      <td>4.086610e+09</td>\n", "      <td>1652.470322</td>\n", "      <td>1352.028333</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2024-12-03</th>\n", "      <td>600519.SH</td>\n", "      <td>1500.536117</td>\n", "      <td>1505.360675</td>\n", "      <td>1492.738055</td>\n", "      <td>1503.233931</td>\n", "      <td>1501.520720</td>\n", "      <td>2480662.0</td>\n", "      <td>3.777197e+09</td>\n", "      <td>1651.672793</td>\n", "      <td>1351.368648</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2024-12-04</th>\n", "      <td>600519.SH</td>\n", "      <td>1496.666624</td>\n", "      <td>1500.201351</td>\n", "      <td>1486.751664</td>\n", "      <td>1496.597702</td>\n", "      <td>1503.233931</td>\n", "      <td>2392389.0</td>\n", "      <td>3.626566e+09</td>\n", "      <td>1653.553386</td>\n", "      <td>1352.914476</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2024-12-05</th>\n", "      <td>600519.SH</td>\n", "      <td>1487.598423</td>\n", "      <td>1494.490650</td>\n", "      <td>1486.751664</td>\n", "      <td>1487.736268</td>\n", "      <td>1496.597702</td>\n", "      <td>1614147.0</td>\n", "      <td>2.441318e+09</td>\n", "      <td>1646.257472</td>\n", "      <td>1346.937932</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2024-12-06</th>\n", "      <td>600519.SH</td>\n", "      <td>1489.705475</td>\n", "      <td>1515.206713</td>\n", "      <td>1484.851379</td>\n", "      <td>1497.592152</td>\n", "      <td>1487.736268</td>\n", "      <td>3203969.0</td>\n", "      <td>4.883149e+09</td>\n", "      <td>1636.509895</td>\n", "      <td>1338.962641</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2024-12-09</th>\n", "      <td>600519.SH</td>\n", "      <td>1498.586601</td>\n", "      <td>1506.168050</td>\n", "      <td>1489.902396</td>\n", "      <td>1495.416177</td>\n", "      <td>1497.592152</td>\n", "      <td>1979986.0</td>\n", "      <td>3.008174e+09</td>\n", "      <td>1647.350382</td>\n", "      <td>1347.833921</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2024-12-10</th>\n", "      <td>600519.SH</td>\n", "      <td>1545.827889</td>\n", "      <td>1555.408084</td>\n", "      <td>1521.390024</td>\n", "      <td>1522.778315</td>\n", "      <td>1495.416177</td>\n", "      <td>6031210.0</td>\n", "      <td>9.421341e+09</td>\n", "      <td>1644.957795</td>\n", "      <td>1345.874560</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2024-12-11</th>\n", "      <td>600519.SH</td>\n", "      <td>1516.289777</td>\n", "      <td>1531.058833</td>\n", "      <td>1507.408651</td>\n", "      <td>1511.957520</td>\n", "      <td>1522.778315</td>\n", "      <td>2967112.0</td>\n", "      <td>4.569663e+09</td>\n", "      <td>1675.057132</td>\n", "      <td>1370.499499</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2024-12-12</th>\n", "      <td>600519.SH</td>\n", "      <td>1508.432639</td>\n", "      <td>1541.889474</td>\n", "      <td>1505.459135</td>\n", "      <td>1541.692554</td>\n", "      <td>1511.957520</td>\n", "      <td>4193652.0</td>\n", "      <td>6.510548e+09</td>\n", "      <td>1663.153272</td>\n", "      <td>1360.761768</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2024-12-13</th>\n", "      <td>600519.SH</td>\n", "      <td>1526.145660</td>\n", "      <td>1531.048987</td>\n", "      <td>1495.494946</td>\n", "      <td>1495.613098</td>\n", "      <td>1541.692554</td>\n", "      <td>4951197.0</td>\n", "      <td>7.580908e+09</td>\n", "      <td>1695.861809</td>\n", "      <td>1387.523298</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2024-12-16</th>\n", "      <td>600519.SH</td>\n", "      <td>1497.582305</td>\n", "      <td>1505.459135</td>\n", "      <td>1487.450733</td>\n", "      <td>1503.686849</td>\n", "      <td>1495.613098</td>\n", "      <td>3253710.0</td>\n", "      <td>4.945299e+09</td>\n", "      <td>1645.174408</td>\n", "      <td>1346.051788</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2024-12-17</th>\n", "      <td>600519.SH</td>\n", "      <td>1502.495478</td>\n", "      <td>1544.843286</td>\n", "      <td>1497.592152</td>\n", "      <td>1534.012644</td>\n", "      <td>1503.686849</td>\n", "      <td>5417163.0</td>\n", "      <td>8.398945e+09</td>\n", "      <td>1654.055534</td>\n", "      <td>1353.318164</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2024-12-18</th>\n", "      <td>600519.SH</td>\n", "      <td>1533.983106</td>\n", "      <td>1552.720116</td>\n", "      <td>1523.280463</td>\n", "      <td>1547.304795</td>\n", "      <td>1534.012644</td>\n", "      <td>4116226.0</td>\n", "      <td>6.440802e+09</td>\n", "      <td>1687.413909</td>\n", "      <td>1380.611380</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2024-12-19</th>\n", "      <td>600519.SH</td>\n", "      <td>1541.042715</td>\n", "      <td>1543.789760</td>\n", "      <td>1526.460734</td>\n", "      <td>1527.130264</td>\n", "      <td>1547.304795</td>\n", "      <td>2480932.0</td>\n", "      <td>3.862603e+09</td>\n", "      <td>1702.035274</td>\n", "      <td>1392.574315</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2024-12-20</th>\n", "      <td>600519.SH</td>\n", "      <td>1531.130000</td>\n", "      <td>1541.800000</td>\n", "      <td>1520.020000</td>\n", "      <td>1522.000000</td>\n", "      <td>1527.130000</td>\n", "      <td>2815774.0</td>\n", "      <td>4.307426e+09</td>\n", "      <td>1679.840000</td>\n", "      <td>1374.420000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2024-12-23</th>\n", "      <td>600519.SH</td>\n", "      <td>1523.000000</td>\n", "      <td>1538.000000</td>\n", "      <td>1517.000000</td>\n", "      <td>1526.450000</td>\n", "      <td>1522.000000</td>\n", "      <td>2160269.0</td>\n", "      <td>3.300971e+09</td>\n", "      <td>1674.200000</td>\n", "      <td>1369.800000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2024-12-24</th>\n", "      <td>600519.SH</td>\n", "      <td>1525.000000</td>\n", "      <td>1544.000000</td>\n", "      <td>1521.590000</td>\n", "      <td>1538.820000</td>\n", "      <td>1526.450000</td>\n", "      <td>2210492.0</td>\n", "      <td>3.394160e+09</td>\n", "      <td>1679.100000</td>\n", "      <td>1373.810000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2024-12-25</th>\n", "      <td>600519.SH</td>\n", "      <td>1538.800000</td>\n", "      <td>1538.800000</td>\n", "      <td>1526.100000</td>\n", "      <td>1530.000000</td>\n", "      <td>1538.820000</td>\n", "      <td>1712339.0</td>\n", "      <td>2.621062e+09</td>\n", "      <td>1692.700000</td>\n", "      <td>1384.940000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2024-12-26</th>\n", "      <td>600519.SH</td>\n", "      <td>1534.000000</td>\n", "      <td>1538.780000</td>\n", "      <td>1523.000000</td>\n", "      <td>1527.790000</td>\n", "      <td>1530.000000</td>\n", "      <td>1828651.0</td>\n", "      <td>2.798840e+09</td>\n", "      <td>1683.000000</td>\n", "      <td>1377.000000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2024-12-27</th>\n", "      <td>600519.SH</td>\n", "      <td>1528.900000</td>\n", "      <td>1536.000000</td>\n", "      <td>1519.500000</td>\n", "      <td>1528.970000</td>\n", "      <td>1527.790000</td>\n", "      <td>2075932.0</td>\n", "      <td>3.170191e+09</td>\n", "      <td>1680.570000</td>\n", "      <td>1375.010000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2024-12-30</th>\n", "      <td>600519.SH</td>\n", "      <td>1533.970000</td>\n", "      <td>1543.960000</td>\n", "      <td>1525.000000</td>\n", "      <td>1525.000000</td>\n", "      <td>1528.970000</td>\n", "      <td>2512982.0</td>\n", "      <td>3.849543e+09</td>\n", "      <td>1681.870000</td>\n", "      <td>1376.070000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2024-12-31</th>\n", "      <td>600519.SH</td>\n", "      <td>1525.400000</td>\n", "      <td>1545.000000</td>\n", "      <td>1522.010000</td>\n", "      <td>1524.000000</td>\n", "      <td>1525.000000</td>\n", "      <td>3935445.0</td>\n", "      <td>6.033540e+09</td>\n", "      <td>1677.500000</td>\n", "      <td>1372.500000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2025-01-02</th>\n", "      <td>600519.SH</td>\n", "      <td>1524.000000</td>\n", "      <td>1524.490000</td>\n", "      <td>1480.000000</td>\n", "      <td>1488.000000</td>\n", "      <td>1524.000000</td>\n", "      <td>5002870.0</td>\n", "      <td>7.490884e+09</td>\n", "      <td>1676.400000</td>\n", "      <td>1371.600000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2025-01-03</th>\n", "      <td>600519.SH</td>\n", "      <td>1494.500000</td>\n", "      <td>1494.990000</td>\n", "      <td>1467.010000</td>\n", "      <td>1475.000000</td>\n", "      <td>1488.000000</td>\n", "      <td>3262836.0</td>\n", "      <td>4.836610e+09</td>\n", "      <td>1636.800000</td>\n", "      <td>1339.200000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2025-01-06</th>\n", "      <td>600519.SH</td>\n", "      <td>1453.000000</td>\n", "      <td>1462.660000</td>\n", "      <td>1432.800000</td>\n", "      <td>1440.000000</td>\n", "      <td>1475.000000</td>\n", "      <td>4425512.0</td>\n", "      <td>6.392480e+09</td>\n", "      <td>1622.500000</td>\n", "      <td>1327.500000</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["           stock_code         open         high          low        close  \\\n", "datetime                                                                    \n", "2024-12-02  600519.SH  1502.505324  1506.433893  1491.773143  1501.520720   \n", "2024-12-03  600519.SH  1500.536117  1505.360675  1492.738055  1503.233931   \n", "2024-12-04  600519.SH  1496.666624  1500.201351  1486.751664  1496.597702   \n", "2024-12-05  600519.SH  1487.598423  1494.490650  1486.751664  1487.736268   \n", "2024-12-06  600519.SH  1489.705475  1515.206713  1484.851379  1497.592152   \n", "2024-12-09  600519.SH  1498.586601  1506.168050  1489.902396  1495.416177   \n", "2024-12-10  600519.SH  1545.827889  1555.408084  1521.390024  1522.778315   \n", "2024-12-11  600519.SH  1516.289777  1531.058833  1507.408651  1511.957520   \n", "2024-12-12  600519.SH  1508.432639  1541.889474  1505.459135  1541.692554   \n", "2024-12-13  600519.SH  1526.145660  1531.048987  1495.494946  1495.613098   \n", "2024-12-16  600519.SH  1497.582305  1505.459135  1487.450733  1503.686849   \n", "2024-12-17  600519.SH  1502.495478  1544.843286  1497.592152  1534.012644   \n", "2024-12-18  600519.SH  1533.983106  1552.720116  1523.280463  1547.304795   \n", "2024-12-19  600519.SH  1541.042715  1543.789760  1526.460734  1527.130264   \n", "2024-12-20  600519.SH  1531.130000  1541.800000  1520.020000  1522.000000   \n", "2024-12-23  600519.SH  1523.000000  1538.000000  1517.000000  1526.450000   \n", "2024-12-24  600519.SH  1525.000000  1544.000000  1521.590000  1538.820000   \n", "2024-12-25  600519.SH  1538.800000  1538.800000  1526.100000  1530.000000   \n", "2024-12-26  600519.SH  1534.000000  1538.780000  1523.000000  1527.790000   \n", "2024-12-27  600519.SH  1528.900000  1536.000000  1519.500000  1528.970000   \n", "2024-12-30  600519.SH  1533.970000  1543.960000  1525.000000  1525.000000   \n", "2024-12-31  600519.SH  1525.400000  1545.000000  1522.010000  1524.000000   \n", "2025-01-02  600519.SH  1524.000000  1524.490000  1480.000000  1488.000000   \n", "2025-01-03  600519.SH  1494.500000  1494.990000  1467.010000  1475.000000   \n", "2025-01-06  600519.SH  1453.000000  1462.660000  1432.800000  1440.000000   \n", "\n", "              pre_close     volume        amount   high_limit    low_limit  \n", "datetime                                                                    \n", "2024-12-02  1502.249327  2681954.0  4.086610e+09  1652.470322  1352.028333  \n", "2024-12-03  1501.520720  2480662.0  3.777197e+09  1651.672793  1351.368648  \n", "2024-12-04  1503.233931  2392389.0  3.626566e+09  1653.553386  1352.914476  \n", "2024-12-05  1496.597702  1614147.0  2.441318e+09  1646.257472  1346.937932  \n", "2024-12-06  1487.736268  3203969.0  4.883149e+09  1636.509895  1338.962641  \n", "2024-12-09  1497.592152  1979986.0  3.008174e+09  1647.350382  1347.833921  \n", "2024-12-10  1495.416177  6031210.0  9.421341e+09  1644.957795  1345.874560  \n", "2024-12-11  1522.778315  2967112.0  4.569663e+09  1675.057132  1370.499499  \n", "2024-12-12  1511.957520  4193652.0  6.510548e+09  1663.153272  1360.761768  \n", "2024-12-13  1541.692554  4951197.0  7.580908e+09  1695.861809  1387.523298  \n", "2024-12-16  1495.613098  3253710.0  4.945299e+09  1645.174408  1346.051788  \n", "2024-12-17  1503.686849  5417163.0  8.398945e+09  1654.055534  1353.318164  \n", "2024-12-18  1534.012644  4116226.0  6.440802e+09  1687.413909  1380.611380  \n", "2024-12-19  1547.304795  2480932.0  3.862603e+09  1702.035274  1392.574315  \n", "2024-12-20  1527.130000  2815774.0  4.307426e+09  1679.840000  1374.420000  \n", "2024-12-23  1522.000000  2160269.0  3.300971e+09  1674.200000  1369.800000  \n", "2024-12-24  1526.450000  2210492.0  3.394160e+09  1679.100000  1373.810000  \n", "2024-12-25  1538.820000  1712339.0  2.621062e+09  1692.700000  1384.940000  \n", "2024-12-26  1530.000000  1828651.0  2.798840e+09  1683.000000  1377.000000  \n", "2024-12-27  1527.790000  2075932.0  3.170191e+09  1680.570000  1375.010000  \n", "2024-12-30  1528.970000  2512982.0  3.849543e+09  1681.870000  1376.070000  \n", "2024-12-31  1525.000000  3935445.0  6.033540e+09  1677.500000  1372.500000  \n", "2025-01-02  1524.000000  5002870.0  7.490884e+09  1676.400000  1371.600000  \n", "2025-01-03  1488.000000  3262836.0  4.836610e+09  1636.800000  1339.200000  \n", "2025-01-06  1475.000000  4425512.0  6.392480e+09  1622.500000  1327.500000  "]}, "execution_count": 1, "metadata": {}, "output_type": "execute_result"}], "source": ["from quantback.market_interface.market_data import get_kline_data\n", "import pandas as pd\n", "\n", "# 测试日线数据\n", "df = get_kline_data(\n", "    '600519.SH',\n", "    '2024-12-01',\n", "    '2025-01-06',\n", "    period='1d',\n", "    adjust_type='pre',\n", "    fields=[\n", "        'open',\n", "        'high',\n", "        'low',\n", "        'close',\n", "        'pre_close',\n", "        'volume',\n", "        'amount',\n", "        'high_limit',\n", "        'low_limit',\n", "    ],\n", ")\n", "df\n"]}], "metadata": {"kernelspec": {"display_name": ".venv", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.9"}}, "nbformat": 4, "nbformat_minor": 5}