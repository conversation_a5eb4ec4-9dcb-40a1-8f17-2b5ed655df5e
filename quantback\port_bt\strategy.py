"""
策略基类 - 面向对象的策略设计
参考主流回测框架（Backtrader、QuantConnect Lean、vn.py）的架构模式
"""

import logging
import sys
from datetime import datetime, time
from types import SimpleNamespace
from typing import TYPE_CHECKING, Any, Callable, Dict, List, Optional, Union

import pandas as pd

from quantback.log_setup import ColorizedFormatter
from quantback.market_interface.market_data import get_kline_data

from .orders import MarketOrderStyle
from .portfolio import OrderCost, Portfolio

if TYPE_CHECKING:
    from .orders import OrderStyle

# 策略函数类型定义
DailyFunc = Callable[[], None]


class Strategy:
    """
    策略基类

    所有策略都应该继承这个基类，并实现initialize方法。
    策略通过self.order()、self.portfolio等方式访问交易和持仓功能。
    """

    def __init__(self, initial_cash: float = 1000000.0):
        """
        初始化策略

        Args:
            initial_cash: 初始资金
        """
        # 基本信息
        self.current_dt: Optional[datetime] = None
        self.previous_date: Optional[datetime] = None

        # 投资组合信息
        self.portfolio = Portfolio(initial_cash, context=self)

        # 基准
        self.benchmark: Optional[str] = None

        # 交易成本配置
        self.order_costs: Dict[str, Any] = {}

        # 全局状态实例，类似聚宽的g对象
        self.g = SimpleNamespace()

        # 定时任务列表
        self._scheduled_tasks: List = []

        # 日志设置
        self.log = logging.getLogger(f'{self.__class__.__module__}.{self.__class__.__name__}')
        self._setup_logger()

    def _setup_logger(self, log_level: int = logging.INFO):
        """设置策略专用的日志器"""
        # 防止重复添加handler
        if self.log.handlers:
            return

        # 设置日志级别
        self.log.setLevel(log_level)

        def filter_func(record):
            tradetime = (
                self.current_dt.strftime('%Y-%m-%d %H:%M:%S') if self.current_dt else 'Initializing'
            )
            record.tradetime = tradetime
            return True

        self.log.addFilter(filter_func)

        # 创建console handler
        console_handler = logging.StreamHandler(sys.stdout)
        console_handler.setLevel(log_level)
        # 设置格式化器
        console_handler.setFormatter(
            ColorizedFormatter('[BT %(tradetime)s %(colored_levelname)s] %(message)s')
        )

        # 添加handler到logger
        self.log.addHandler(console_handler)

        # 不向上层传递日志
        self.log.propagate = False

    def initialize(self):
        """
        策略初始化方法

        子类必须重写此方法，在此方法中：
        - 设置基准指数
        - 设置交易成本
        - 设置策略参数
        - 注册定时任务
        """
        raise NotImplementedError('子类必须实现initialize方法')

    def run_daily(self, func: DailyFunc, time: Union[str, time] = '9:30'):
        """
        注册每日定时执行的函数

        Args:
            func: 要执行的函数（策略实例的方法）
            time: 执行时间，可以是:
                - 'every_minute': 每分钟执行一次（在交易时间内）
                - '9:30': 具体时间点执行
                - time对象: 具体时间点执行

        Examples:
            self.run_daily(self.handle_data, time='9:30')  # 每天9:30执行
            self.run_daily(self.handle_data, time='every_minute')  # 每分钟执行
        """
        task_info = (func, time)
        self._scheduled_tasks.append(task_info)

    def get_scheduled_tasks(self) -> List:
        """获取所有定时任务"""
        return self._scheduled_tasks.copy()

    def clear_scheduled_tasks(self):
        """清空所有定时任务"""
        self._scheduled_tasks.clear()

    def set_benchmark(self, security: str):
        """
        设置基准指数

        Args:
            security: 基准指数代码，如 '000300.SH'
        """
        self.benchmark = security
        self.log.info(f'设置基准指数: {security}')

    def set_order_cost(self, cost: OrderCost, type: str):
        """
        设置交易成本

        Args:
            cost: OrderCost 对象，包含各种费率配置
            type: 类型，目前支持 'stock' (股票)
        """
        # 验证类型参数
        if type not in ['stock', 'fund']:
            raise ValueError(f"不支持的类型: {type}，目前只支持 'stock' 和 'fund'")

        # 设置交易成本
        if not hasattr(self, 'order_costs') or self.order_costs is None:
            self.order_costs = {}

        self.order_costs[type] = cost

        self.log.info(f'设置{type}交易成本: {cost}')
        self.log.info(f'  买入佣金: 万分之{cost.open_commission * 10000:.1f}')
        self.log.info(f'  卖出佣金: 万分之{cost.close_commission * 10000:.1f}')
        self.log.info(f'  卖出印花税: 千分之{cost.close_tax * 1000:.1f}')
        self.log.info(f'  最低佣金: {cost.min_commission}元')

    def order(
        self, security: str, volume: int, style: Optional['OrderStyle'] = None
    ) -> Optional[str]:
        """
        下单交易

        Args:
            security: 股票代码
            volume: 委托数量，正数买入，负数卖出
            style: 订单样式，None表示市价单

        Returns:
            Optional[str]: 订单ID
        """
        # 如果没有指定样式，使用市价单
        if style is None:
            style = MarketOrderStyle()
        self.log.debug(f'order({security}, {volume}, {style})')
        return self.portfolio.order(security, volume, style)

    def order_value(
        self, security: str, value: float, style: Optional['OrderStyle'] = None
    ) -> Optional[str]:
        """
        按委托金额下单

        Args:
            security: 股票代码
            value: 委托金额，正数买入，负数卖出
            style: 订单样式，None表示市价单

        Returns:
            Optional[str]: 订单ID
        """
        # 如果没有指定样式，使用市价单
        if style is None:
            style = MarketOrderStyle()
        self.log.debug(f'order_value({security}, {value}, {style})')
        return self.portfolio.order_value(security, value, style)

    def order_target(
        self, security: str, volume: int, style: Optional['OrderStyle'] = None
    ) -> Optional[str]:
        """
        调整持仓到目标数量

        Args:
            security: 股票代码
            volume: 目标持仓数量
            style: 订单样式，None表示市价单

        Returns:
            Optional[str]: 订单ID
        """
        # 如果没有指定样式，使用市价单
        if style is None:
            style = MarketOrderStyle()
        self.log.debug(f'order_target({security}, {volume}, {style})')
        return self.portfolio.order_target(security, volume, style)

    def order_target_value(
        self, security: str, value: float, style: Optional['OrderStyle'] = None
    ) -> Optional[str]:
        """
        调整持仓到目标金额

        Args:
            security: 股票代码
            value: 目标持仓金额
            style: 订单样式，None表示市价单

        Returns:
            Optional[str]: 订单ID
        """
        # 如果没有指定样式，使用市价单
        if style is None:
            style = MarketOrderStyle()
        # 打印函数的调用参数
        self.log.debug(f'order_target_value({security}, {value}, {style})')
        return self.portfolio.order_target_value(security, value, style)

    def get_current_market_data(self, symbol: str) -> Optional[Dict]:
        """
        获取当前时间的市场数据

        Args:
            symbol: 股票代码

        Returns:
            Optional[Dict]: 市场数据
        """
        return self.portfolio.get_current_market_data(symbol, self.current_dt)

    def cancel_order(self, order_id: str) -> bool:
        """
        取消订单

        Args:
            order_id: 订单ID

        Returns:
            bool: 是否成功
        """
        return self.portfolio.cancel_portfolio_order(order_id)

    def get_price(
        self,
        stock_code: str,
        start_date: Optional[Union[str, datetime]] = None,
        end_date: Optional[Union[str, datetime]] = None,
        count: int = 0,
        period: str = '1d',
        adjust_type: str = 'pre',
        fields: Optional[List[str]] = None,
        start_inclusive: bool = True,
    ) -> Optional[pd.DataFrame]:
        """
        获取K线数据（get_kline_data的包装函数）
        这是get_kline_data函数的别名，保持与聚宽API的兼容性
        """
        return get_kline_data(
            stock_code=stock_code,
            start_date=start_date,
            end_date=end_date,
            count=count,
            period=period,
            adjust_type=adjust_type,
            fields=fields,
            start_inclusive=start_inclusive,
        )
